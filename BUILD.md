# Build Instructions

## Environment Configuration

Aplicația folosește fișiere de environment pentru configurare:

- `.env.development` - Configurație pentru development
- `.env.production` - Configurație pentru production

## Comenzi de Build

### Development Build
```bash
npm run build:dev
```
Această comandă va crea `loadFirmware-dev.exe` folosind configurația din `.env.development`.

### Production Build
```bash
npm run build:prod
```
Această comandă va crea `loadFirmware-prod.exe` folosind configurația din `.env.production`.

### Build Generic
```bash
npm run build
```
Această comandă va afișa un mesaj care îți spune să specifici environment-ul.

## Variabile de Environment

Fiecare fișier .env conține:
- `MONGO_URL` - URL-ul pentru MongoDB
- `APP_VERSION` - Versiunea aplicației
- `DOWNLOAD_URL` - URL-ul pentru download

## Notă Importantă

**Nu mai folosi comanda `pkg --targets node14-win-x64 .` direct!**

Folosește întotdeauna una din comenzile npm de mai sus pentru a te asigura că environment-ul corect este setat.
