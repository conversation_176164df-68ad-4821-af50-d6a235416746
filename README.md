# Firmware App

## Environment Configuration

The application uses environment files for configuration:

- `.env.development` - Configuration for development environment
- `.env.staging` - Configuration for staging environment
- `.env.production` - Configuration for production environment

Each .env file contains:
- `MONGO_URL` - MongoDB connection URL
- `APP_VERSION` - Application version
- `DOWNLOAD_URL` - Download URL

## Build Instructions

### Important Note
**Do not use the `pkg --targets node14-win-x64 .` command directly anymore!**

Always use one of the npm commands below to ensure the correct environment is set.

### Available Build Commands

#### Development Build
```bash
npm run build:dev
```
This command will create `loadFirmware-dev.exe` using the configuration from `.env.development`.

#### Staging Build
```bash
npm run build:staging
```
This command will create `loadFirmware-staging.exe` using the configuration from `.env.staging`.

#### Production Build
```bash
npm run build:prod
```
This command will create `loadFirmware-prod.exe` using the configuration from `.env.production`.

#### Generic Build
```bash
npm run build
```
This command will display a message telling you to specify the environment.

## Deployment Steps

Before you do the steps below, make sure the TFTP files are updated on gitlab.

1. Do a Pull request to make sure you have the latest TFTP files
2. Go to folder with app.js
3. Open command line
4. **Run the appropriate build command based on your target environment:**
   - For development: `npm run build:dev`
   - For staging: `npm run build:staging`
   - For production: `npm run build:prod`
5. Create a zip file (app.zip) with the generated .exe file and bindings.node files
6. Go to nms server on html/download/app/
7. Create a new folder with new version ex: v-0.4
8. Copy zip file in it
9. Go to mongoDB to latestVersion collection
10. Add new Document:
```json
{
    "_id": ObjectID(),
    "version": "0.4"
}
```
