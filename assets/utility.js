var conf = require('./config');
var Binary = require('mongodb').Binary;
var MongoClient = require('mongodb').MongoClient;
// Înlocuit serial-number cu systeminformation
const si = require('systeminformation');
var date_ob = new Date();
//current date
var date = ("0" + date_ob.getDate()).slice(-2);
var month = ("0" + (date_ob.getMonth() + 1)).slice(-2);
var year = date_ob.getFullYear();
var hours = date_ob.getHours();
var minutes = date_ob.getMinutes();
var seconds = date_ob.getSeconds();

const { Select } = require('enquirer');
const { SerialPort } = require('serialport');
const { DelimiterParser } = require('@serialport/parser-delimiter');
//const InterByteTimeout = require('@serialport/parser-inter-byte-timeout')
//const InterByteTimeout = SerialPort.parsers.interByteParser
const { ReadlineParser } = require('@serialport/parser-readline');
const PromptList = require('prompt-list');
const tftp = require('tftp');
const mongoURL = conf.mongoURL;
const appVersion = conf.appVersion;
const sessionID = date+month+year+hours+minutes+seconds;
console.log('sessionID:',sessionID);

var writemongodberr = function(msg,err){

	var dbRecord = {};
	dbRecord.date = new Date();
	dbRecord.sessionID = sessionID;
    dbRecord.errorMsg = msg;
    dbRecord.error = err;

	MongoClient.connect(mongoURL, {useUnifiedTopology: true, useNewUrlParser: true }, function(err, db) {
	      if (err) throw err;
	      var dbo = db.db("firmwareLoadBeta");

	      dbo.collection("errorLog").insertOne(dbRecord, function(err, res) {
	        if (err) throw err;
	        console.log("Error logged in database");
	        db.close();
	      });
	    });
}

var other = function(){
	var fs = require('fs');
	var util = require('util');
	try{
	    // file not presenet
		var data = fs.readFileSync('sample1.html');

	} catch (err){
		writemongodberr('tst',err);
		writemongodberr('tst1',err);
	}
}

var listPorts = function() {

//	var html = '<option value="" disabled selected>Please select a Port</option>';
//	var counter = 0;
//
//	const ports = await SerialPort.list();
//	for (const [single_key, port] of Object.entries(ports)) {
//				  console.log(`${port.path}\t${port.pnpId || ''}\t${port.manufacturer || ''}`)
//			  	  counter++;
//			  	  var val = `[${counter}]\t${port.path}\t${port.pnpId || ''}\t${port.manufacturer || ''}`;
//			  	  html += '<option value="">'+port.path+'</option>';
//	}

//
//
//    SerialPort.list().then(
//     ports => {
//      ports.forEach(port => {
//    	  console.log(`${port.path}\t${port.pnpId || ''}\t${port.manufacturer || ''}`)
//    	  counter++;
//    	  var val = `[${counter}]\t${port.path}\t${port.pnpId || ''}\t${port.manufacturer || ''}`;
//    	  html += '<option value="">'+port.path+'</option>';
//      })
//     },
//     err => {
//      console.error('Error listing ports', err);
//      writemongodberr('Error listing ports',err);
//     }
//    )
//
//    return html;
}


var sendUbootCommandERROR = function(commandString){
//	   throw new Error('sendUbootCommand timeout');
//	    var commandString = commandString.replace("\", "&#92;");
		writemongodberr('Error sendUbootCommand',commandString);
		console.error('Error sendUbootCommand: ' + commandString);

   }

var sendUbootCommand = function(port, parser, commandString, timeout){
  return new Promise(function(resolve, reject) {
    const timeoutId = setTimeout(() => sendUbootCommandERROR(commandString), timeout);
    port.write(commandString, function () {
        console.log('message written')
        parser.on('data', (data) => {  // consider using port.once instead
            //console.log("rec %s",data)
            clearTimeout(timeoutId);
            resolve (data)
        })
    })
  });
}

var serialCommandERROR = function(sendString,expectedString){
	writemongodberr('Error serialCommand',sendString+' '+expectedString);
//	var sendString = sendString.replace("\", "&#92;");
//	var expectedString = expectedString.replace("\", "&#92;");
	console.error('Error serialCommand: ' + sendString+' '+expectedString);
//  throw new Error('serialCommand timeout');
//  writemongodberr('serialCommand timeout','serialCommand timeout');
 }

var getPCSerial = function(){
  return new Promise(function(resolve, reject) {
	  si.uuid()
		  .then(data => {
			  // We use hardware UUID as serial number
			  console.log('Serial Number:', data.hardware);
			  resolve(data.hardware);
		  })
		  .catch(error => {
			  writemongodberr('getPCSerial',error);
    		console.error('getPCSerial: ' + error);
			  resolve('unknown');
		  })
    // serialNumber.preferUUID = true;
    // serialNumber(function (err, value) {
    //   //console.log(value);
    // 	if(err){
    // 		writemongodberr('getPCSerial',err);
    // 		console.error('getPCSerial: ' + err);
    // 	}
    // 	resolve(value);
    // });
  });
}

var serialCommand = function(port, sendString, expectedString, timeout, byteTimeout){
  return new Promise(function(resolve, reject) {

//		console.log('serialCommand start');
	    const timeoutId = setTimeout(() => serialCommandERROR(sendString,expectedString), timeout);

	    const serialParser = new DelimiterParser({ delimiter: expectedString });
	    port.pipe(serialParser);  // when is this closed or finished?

	    port.write(sendString, function (err)  {
	        //const interByteParser = port.pipe(new InterByteTimeout({interval: byteTimeout}))
	        //interByteParser.on('data', reject("interbyte timeout") )
	        //console.log('message written')
	    	if (err) {
	    	    console.error('Error on write: ' + err.message)
	    	}
	        serialParser.on('data', (data) => {  // consider using port.once instead
//	            console.log("rec %s",data)
	            clearTimeout(timeoutId);
	            resolve (data)
	            serialParser.destroy();   // not sure if this is right or end?  just need to stop the pipe.
	        })
	    })

//	    port.on('error', function(err) {
//		  console.log('Port Error: ', err.message)
//		})


  });
}

var serialDynamicCommand = function(port, sendString, expectedString, timeout, byteTimeout){
	  return new Promise(function(resolve, reject) {

		//	console.log('serialCommand start');
		    const timeoutId = setTimeout(() => serialCommandERROR(sendString,expectedString), timeout);

		    const serialParser = new DelimiterParser({ delimiter: expectedString });
		    port.pipe(serialParser);  // when is this closed or finished?

		    port.write(sendString, function (err)  {
		        //const interByteParser = port.pipe(new InterByteTimeout({interval: byteTimeout}))
		        //interByteParser.on('data', reject("interbyte timeout") )
		        //console.log('message written')
		    	if (err) {
		    	    console.error('Error on Dynamic IP write : ' + err.message)
		    	}
		        serialParser.on('data', (data) => {  // consider using port.once instead
//		            console.log("rec %s",data)
		            clearTimeout(timeoutId);
		            resolve (data)
		            serialParser.destroy();   // not sure if this is right or end?  just need to stop the pipe.
		        })
		    })

		    port.on('error', function(err) {
			  console.error('Port Error on Dynamic IP: ' + err.message)
			})


	  });
	}

var dbInsert = function(record){
	  return new Promise(function(resolve, reject) {
	    MongoClient.connect(mongoURL, {useUnifiedTopology: true, useNewUrlParser: true }, function(err, db) {
			if (err) {
				throw err;
				writemongodberr('MongoClient.connect',err);
				console.error('MongoClient.connect: ' + err);
			}
	      var dbo = db.db("firmwareLoadBeta");
	      //var myobj = { name: "Company Inc", address: "Highway 37" };
	      dbo.collection("initialLoad").insertOne(record, function(err, res) {
	        if (err) {
	        	throw err;
	        	writemongodberr('initialLoad',err);
	        	console.error('initialLoad: ' + err);
	        }
	        console.log("1 document inserted");
	        resolve (true);
	        db.close();
	      });
	    });

	  });
	}

var dbInsertAddresses = function(record){
	  return new Promise(function(resolve, reject) {
	    MongoClient.connect(mongoURL, {useUnifiedTopology: true, useNewUrlParser: true }, function(err, db) {
			if (err) {

				writemongodberr('MongoClient.connect',err);
				console.error('MongoClient.connect: ' + err);
//				throw err;
				resolve (false);
			}
	      var dbo = db.db("firmwareLoadBeta");
	      //var myobj = { name: "Company Inc", address: "Highway 37" };
	      dbo.collection("macAddresses").insertOne(record, function(err, res) {
	        if (err) {

	        	writemongodberr('macAddresses',err);
	        	console.error('macAddresses: ' + err);
	        	resolve (false);
	        }
	        console.log("1 address inserted");
	        resolve (true);
	        db.close();
	      });
	    });

	  });
	}


var verifyVersion = function(version){
  return new Promise(function(resolve, reject) {
    MongoClient.connect(mongoURL,  {useUnifiedTopology: true, useNewUrlParser: true }, function(err, db) {
    	if (err) {
			throw err;
			writemongodberr('MongoClient.connect',err);
			console.error('MongoClient.connect ' + err);
		}
      var dbo = db.db("firmwareLoadBeta");

      var query = { version : version };
      dbo.collection("approvedVersions").find(query).toArray(function(err, result) {
    	  if (err) {
				throw err;
				writemongodberr('approvedVersions',err);
				console.error('approvedVersions ' + err);
			}
        //console.log(result,result.length);
        db.close();
        if (result.length > 0){
          resolve (true);
        }else{
          resolve(false);
        }
      });
    });

  });
}

var parseMAC = function(macIn){
	  return macIn.split(" ")[1] + ":" + macIn.split(" ")[2] + ":" + macIn.split(" ")[3] + ":" + macIn.split(" ")[4] + ":" +macIn.split(" ")[5] + ":" +macIn.split(" ")[6]
	  //return macIn.split(" ")[1] + macIn.split(" ")[2] + macIn.split(" ")[3] + macIn.split(" ")[4] + macIn.split(" ")[5] +  macIn.split(" ")[6]
	}

var calcXOR = function(oldCRC,oldMAC,newMAC){
	  var newCRC;
	  var oldMACnos;
	  var newMACnos;

	  newCRC = parseInt(oldCRC,16)

	  console.log("newCRC %s",newCRC.toString(16));

	  oldMACnos = oldMAC.replace(new RegExp(':', 'g'),'');
	  newCRC ^= parseInt(oldMACnos.substr(0,4)  ,16)
	  console.log("newCRC %s",newCRC.toString(16));
	  newCRC ^= parseInt(oldMACnos.substr(4,4)  ,16)
	  console.log("newCRC %s",newCRC.toString(16));
	  newCRC ^= parseInt(oldMACnos.substr(8,4) ,16)
	  console.log("newCRC %s",newCRC.toString(16));

	  newMACnos = newMAC.replace(new RegExp(':', 'g'),'');
	  newCRC ^= parseInt(newMACnos.substr(0,4)  ,16)
	  console.log("newCRC %s",newCRC.toString(16));
	  newCRC ^= parseInt(newMACnos.substr(4,4)  ,16)
	  console.log("newCRC %s",newCRC.toString(16));
	  newCRC ^= parseInt(newMACnos.substr(8,4),16)
	  console.log("newCRC %s",newCRC.toString(16));

	  return newCRC;
	}

var MACWriteRam = function(ramAddress,macAddress){
	  var curOffset;
	  var curByte;
	  var command;
	  //var MACnos;



	  //MACnos = macAddress.replace(new RegExp(':', 'g'),'');


	  command = ""

	  for (curOffset = 0; curOffset < 6; curOffset++) {
	    curByte = macAddress.split(":")[curOffset]
	    command += " mw.b 0x" + (ramAddress + curOffset).toString(16) + " 0x" + curByte
	    if (5 != curOffset){
	      command += "; "
	    }


	  }

	  command += "\n"

	  console.log("command %s",command)
	  return command

	}

function verifyLatestVersion(version){
	  return new Promise(function(resolve, reject) {

		  MongoClient.connect(mongoURL,  {useUnifiedTopology: true, useNewUrlParser: true }, function(err, db) {
		      if (err) throw err;
		      var dbo = db.db("firmwareLoadBeta");

		      dbo.collection('latestVersion', function(err, collection) {
	  		  collection
	  		    .find()
	  		    .sort({$natural: -1})
	  		    .limit(1)
	  		    .next()
	  		    .then(
	  		      function(doc) {

	  		        if(doc.version != version){
	  		        	console.log('New version available!');
	  		        	console.log('Get latest version: ',doc.version);
	  		        	var url = conf.downloadurl + '/v-'+doc.version+'/app.zip';
	  		        	require("openurl").open(url);
	  		        	console.log('url: ',url);
	  		        	const sleep = (waitTimeInMs) => new Promise(resolve => setTimeout(resolve, waitTimeInMs));
	  		        	sleep(1000).then(() => {
	  		        		resolve (false);
	  		        	});

	  		        } else {
	  		        	resolve (true);
	  		        }


	  		      },
	  		      function(err) {
	  		    	writemongodberr('getLatestVersion',err);
	  		    	console.error('getLatestVersion ' + err);
	  		      }
	  		    );
	  		});

		    });

	  });
	}

function verifyMacAddress(macaddress){
	  return new Promise(function(resolve, reject) {
		    MongoClient.connect(mongoURL,  {useUnifiedTopology: true, useNewUrlParser: true }, function(err, db) {
		    	if (err) {

					writemongodberr('MongoClient.connect',err);
					console.error('MongoClient.connect ' + err);
					resolve (false);
				}
		      var dbo = db.db("firmwareLoadBeta");

		      var query = { mac_address : macaddress };
		      dbo.collection("macAddresses").find(query).toArray(function(err, result) {
		    	  if (err) {

						writemongodberr('macAddresses',err);
						console.error('macAddresses ' + err);
						resolve (false);
					}
//		        console.log(result,result.length);
		        db.close();
		        if (result.length > 0){
			    	console.log("THIS MacAddress ("+macaddress+") already exist in DATABASE. EXITING");

		        	resolve(false);
		        }else{
		        	console.log("THIS MacAddress ("+macaddress+") is approved by checking for duplicity in DATABASE.");

		            resolve (true);
		        }
		      });
		    });

		  });
}

var isARRAMacAddress = function(macaddress,macaddresstype){
	  return new Promise(function(resolve, reject) {
	    // We no longer need the serialNumber, we check the MAC address directly
	    var hex = macaddress.split(':');
	    var h_0 = hex[0];
	    var h_1 = hex[1];
	    var h_2 = hex[2];

	    if(h_0.toLowerCase() == "f0" && h_1.toLowerCase() == "aa" && h_2.toLowerCase() == "0b"){
		   console.log("THIS ("+macaddresstype+") Device MacAddress ("+macaddress+") Is ARRA. EXITING");
	       resolve(false);
	    }else{
	       console.log("THIS ("+macaddresstype+") MacAddress ("+macaddress+") Is not ARRA Address. APPROVED.");
	       resolve(true);
	    }
	  });
	}

function verifyNewMacAddress(device_address,updated_address,address_type){
	  return new Promise(function(resolve, reject) {
		    MongoClient.connect(mongoURL,  {useUnifiedTopology: true, useNewUrlParser: true }, function(err, db) {
		    	console.log("Start Verify  "+address_type+"; Device address: "+device_address+ " Init Address: " + updated_address);
		    	if (err) {

					writemongodberr('MongoClient.connect',err);
					console.error('MongoClient.connect ' + err);
					resolve(false);
				}
		      var dbo = db.db("firmwareLoadBeta");


		      if(device_address.toLowerCase() != updated_address.toLowerCase()){
		        	console.log("New "+address_type+" MAC address is not updated! Device address: "+device_address);
		        	resolve(false);
		      } else {

		    	  var query = { mac_address : device_address.toLowerCase() };
		    	  var update = {$set: {"status":"used"}};
		    	  var options = { "upsert": true };

			      dbo.collection("macAddresses").updateOne(query, update, options)
			      .then(result => {
			    	    const { matchedCount, modifiedCount } = result;
			    	    if(matchedCount && modifiedCount) {
			    	    	console.log("DB Status for MacAddress ("+device_address+") IS UPDATED.");
			    	    }
			    	  })
			    	  .catch(err => console.error("DB Status for MacAddress ("+device_address+") IS NOT UPDATED. EXITING. Error: "+err));

			      resolve(true);
		      }


		    });

		  });
}


// this could brick the board.  putting it in a seperate function so it can be commented out during testing.
var updateUboot = function(port){

}

function doRequest (){
//	tftp.createClient ({ port: 69 }).put (__filename, function (error){
//		console.error (error); //[Error: (Server) Cannot PUT files]
//		server.close ();
//	});
}

module.exports = {
		writemongodberr: writemongodberr,
		mongoURL: mongoURL,
		appVersion: appVersion,
		updateUboot: updateUboot,
		verifyVersion: verifyVersion,
		verifyLatestVersion: verifyLatestVersion,
		dbInsert: dbInsert,
		serialCommand: serialCommand,
		serialDynamicCommand: serialDynamicCommand,
		getPCSerial: getPCSerial,
		serialCommandERROR: serialCommandERROR,
		sendUbootCommand: sendUbootCommand,
		sendUbootCommandERROR: sendUbootCommandERROR,
		listPorts: listPorts,
		MACWriteRam: MACWriteRam,
		calcXOR: calcXOR,
		parseMAC: parseMAC,
		verifyMacAddress: verifyMacAddress,
		isARRAMacAddress: isARRAMacAddress,
		dbInsertAddresses: dbInsertAddresses,
		verifyNewMacAddress: verifyNewMacAddress,
		other: other
};
