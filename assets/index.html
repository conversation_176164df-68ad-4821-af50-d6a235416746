<!DOCTYPE html>
<html>
    <head>
    
        <!-- Include meta tag to ensure proper rendering and touch zooming -->
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta http-equiv="content-type" content="text/html;charset=utf-8" />
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<link rel="icon" href="static/favicon.ico" type="image/x-icon" />
	<title>ARRA Application</title>		
        
		<link rel="stylesheet" href="http://localhost:8080/static/style.css">	
		<script src="http://localhost:8080/static/socket.io.js"></script>
		<script src="http://localhost:8080/static/jquery-1.11.1.js"></script>
		
    </head>
    <body class="bg_theme bg_theme_arra">
		<header class="header">
			<a class="logo" href="javascript:void(0);">
				<img class="wifi" src="http://localhost:8080/static/logo-arra.png" alt="Arra Logo">
			</a>
		</header>
		<main class="main">
			
			<div class="search-section">
				
				<div class="modal-button install-button install-section init-section">
					<a href="javascript:void(0);" id="toggle-modal" onclick="showfirmareupdatesection()">Firmware Update</a><br /><br />
					<a href="javascript:void(0);" id="toggle-modal" onclick="showmacupdatesection()">Mac update</a>
				</div>
			
			
				<div class="ar-custom-select install-section firmare-section" style="display: none;">
					<select  name="c_address" id="current_address">
					</select>
					<input type="hidden" id="slected_current_address" />
				</div>
				
				<div class="ar-custom-select install-section firmare-section" style="margin-bottom: 20px; margin-left: 10px; display: none;">
					<select  name="c_ports" id="current_ports">
					</select>
					<input type="hidden" id="slected_current_port" />
				</div>
				
				<div class="ar-custom-select install-section firmare-section" style="margin-bottom: 20px; margin-left: 10px; display: none;">
					<select  name="c_static_ip" id="c_static_ip">
						<option value="" disabled selected>Please select a IP Type</option>
						<option value="static_ip" >Static IP</option>
						<option value="dhcp" >DHCP</option>
					</select>
					<input type="hidden" id="slected_c_static_ip" />
				</div>
				
				<form class="search-input firmware-search-input install-section" autocomplete="off" action="javascript:void(0);" style="display:none;">				
					<input id="static_ip" type="text" placeholder="Enter Static IP" value="************">
				</form>

				<div class="modal-button install-section firmare-section" style="display: none;">
					<a href="javascript:void(0);" id="toggle-modal" class="back-button" onclick="backtoinitsection()"> Back</a>
					<a href="javascript:void(0);" id="toggle-modal" onclick="startproccess()">Install</a>
				</div>
				<div class="modal-button back-to-firmare-dashboard-button" style="display: none;">
					<a href="javascript:void(0);" id="toggle-modal" class="back-button" onclick="backtoinitsection()"> Back</a>
				</div>
				<div class="ar-custom-select install-section mac-section" style="margin-bottom: 20px; margin-left: 10px; display: none;">
					<select  name="mac_c_ports" id="mac_current_ports">						
					</select>
					<input type="hidden" id="slected_mac_current_port" />
				</div>				
				
				<form class="search-input mac-search-input install-section mac-section-address" autocomplete="off" action="javascript:void(0);" style="display:none;">				
					<input id="mac_static_ip" type="text" class="mac-search-input-text" placeholder="Enter MAC Address" value="">
				</form>

				<div class="modal-button install-section mac-section" style="display: none;">
					<a href="javascript:void(0);" id="toggle-modal" class="back-button" onclick="backtoinitsection()"> Back</a>
					<a href="javascript:void(0);" id="toggle-modal" onclick="startmacproccess()">Install</a>
				</div>
				<div class="modal-button exit-button" style="display: none;">
					<a href="javascript:void(0);" id="toggle-modal" onclick="closeproccess()">Close</a>
				</div>
				<div class="modal-button back-to-mac-dashboard-button" style="display: none;">
					<a href="javascript:void(0);" id="toggle-modal" class="back-button" onclick="backtomacdashboard()"> Back</a>
				</div>
				<div class="mess">
				</div>
				
			</div>

			<div id="modal-vouchers-payment">		
			</div>
			
			
		</main>
		<footer class="footer">
						
			<p class="copyc">Copyright &copy; 2020, ARRA Networks</p>
		</footer>
<script>
var socket = io('http://localhost:9090');
var height = 450;

function checkIPV4(addr) { 
    var result =  
/^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/.test(addr); 
	return result;
} 
function checkIPV6(addr) { 
	var result =  
		/^((?:[0-9A-Fa-f]{1,4}))((?::[0-9A-Fa-f]{1,4}))*::((?:[0-9A-Fa-f]{1,4}))((?::[0-9A-Fa-f]{1,4}))*|((?:[0-9A-Fa-f]{1,4}))((?::[0-9A-Fa-f]{1,4})){7}$/g.test(addr); 
	return result;
} 

function scrollToEl(){

	var div = $( ".mess" );

	div.animate({scrollTop: height}, 500);
    height += div.height();
    
}
function closeproccess(){
	socket.emit('closeproccess');
	$( ".exit-button" ).hide();
}

function checkMacAddress(mac_address){
	
	var res = mac_address.split(":");
	if(res.length == 6){
		return true;
	}
	
	return false;
}

function nextMACaddress( MACaddress )
{
  let hex = MACaddress.split(':').map(x=>parseInt(x,16))
  plusOne(hex.length-1)
  return hex.map(x=>('0'+x.toString(16).toUpperCase()).slice(-2)).join(':')

  function plusOne(p) {
    if (p<0) return
    if (hex[p]==255) { hex[p]=0; plusOne(p-1) }
    else             { hex[p]++               }
  }
} 

function startmacproccess(){
	var input_init = $( "#mac_static_ip" ).val();
	var val_pices = input_init.split(":");

	if($( "#slected_mac_current_port" ).val() == '' || $( "#mac_static_ip" ).val() == '' || val_pices.length != 6){
		alert('You must select an IP address, a Port and a MAC Address!');
	}else {

			obj_val = $( "#mac_static_ip" ).val();
			obj_val = obj_val.toString();
			obj_val = obj_val.replace(/<\/?[^>]+>/gi, '');
			obj_val = jQuery.trim(obj_val);
			
			if(obj_val && obj_val != ''){
				var ip_mac = checkMacAddress(obj_val);
				if(ip_mac){
					
					var first_mac = obj_val;
					var second_mac = nextMACaddress(first_mac);
					var third_mac = nextMACaddress(second_mac);
					var fourth_mac = nextMACaddress(third_mac);
//NEW: This needs to change for 8 mac addresses in sequence.  see comments in startMacAppProccess()
					socket.emit('startmac',$( "#slected_mac_current_port" ).val(),first_mac,second_mac,third_mac,fourth_mac);
					$( ".install-section" ).hide();
					$( ".exit-button" ).hide();
					
				} else {
					alert('You must use a valid MAC Address!');
				}
				
			} else {
				alert('You must use a valid MAC Address!');
			}
		
		
	}
	
}

function startproccess(){
	
	if($( "#slected_current_address" ).val() == '' || $( "#slected_current_port" ).val() == '' || $( "#c_static_ip" ).val() == ''){
		alert('You must select an IP address, a Port and a Static IP!');
	}else {
		
		if($( "#c_static_ip" ).val() == 'static_ip'){
			obj_val = $( "#static_ip" ).val();
			obj_val = obj_val.toString();
			obj_val = obj_val.replace(/<\/?[^>]+>/gi, '');
			obj_val = jQuery.trim(obj_val);
			
			if(obj_val && obj_val != ''){
				var ip_v4 = checkIPV4(obj_val);
				var ip_v6 = checkIPV6(obj_val);
				if(ip_v4 || ip_v6){
					socket.emit('start',$( "#slected_current_address" ).val(),$( "#slected_current_port" ).val(),$( "#c_static_ip" ).val(),obj_val);
					$( ".install-section" ).hide();
				} else {
					alert('You must use a valid Static IP!');
				}
				
			} else {
				alert('You must use a valid Static IP!');
			}
			
		} else {
			
			socket.emit('start',$( "#slected_current_address" ).val(),$( "#slected_current_port" ).val(),$( "#c_static_ip" ).val(),'');
			$( ".install-section" ).hide();
			$( ".exit-button" ).show();
			
		}
		
		
	}
	
}

function showfirmareupdatesection(){
	$( ".init-section" ).hide();
	$( ".mac-section" ).hide();
	$( ".firmare-section" ).show();
	
	
}

function showmacupdatesection(){
	$( ".init-section" ).hide();
	$( ".mac-section" ).show();
	$( ".firmare-section" ).hide();
	$( ".mac-search-input-text" ).val("");
}

function backtomacdashboard(){
	$( ".back-to-mac-dashboard-button" ).hide();
	$( ".init-section" ).hide();	
	$( ".firmare-section" ).hide();
	$( ".mac-section" ).show();
	$( ".mac-section-address" ).show();
	$( ".mess" ).html("");
}

function backtoinitsection(){
	$( ".init-section" ).show();
	$( ".mac-section" ).hide();
	$( ".firmare-section" ).hide();
	$( ".search-input" ).hide();
	$( ".mac-section-address" ).hide();
	$( ".back-to-firmare-dashboard-button" ).hide();
	$( "#c_static_ip, #current_address, #current_ports, #mac_current_ports" ).val("");
	$( ".mess" ).html("");
	
}

$( document ).ready(function() {
	
	socket.emit('getaddresses');
	socket.emit('getports');
	
	
	socket.on("showmacdashboard", function(html) {			
		$( ".exit-button" ).hide();
		$( ".back-to-mac-dashboard-button" ).show();
	});
	socket.on("showfirmaredashboard", function(html) {			
		$( ".exit-button" ).hide();
		$( ".back-to-firmare-dashboard-button" ).show();
	});
	
	socket.on("setaddresses", function(html) {
		
		$( "#current_address" ).empty().html(html);
		
	  
	});
	socket.on("setports", function(html) {
		
		$( "#current_ports" ).empty().html(html);
		$( "#mac_current_ports" ).empty().html(html);
	  
	});	
	
	
	socket.on("writelog", function(mess) {
		
		$( ".mess" ).append( "<pre>"+mess+"</pre>" );
		scrollToEl();
	  
	});
	
	
	
	socket.on("errorlog", function(mess) {
		
		var new_array = jQuery.makeArray( mess );
		
		if (new_array[0]){
			var myJSON = JSON.stringify(mess);
		    $( ".mess" ).append( "<pre><span style='color:red'>Error:</span> "+myJSON+"</pre>" );
		    
		} else {
			$( ".mess" ).append( "<pre><span style='color:red'>Error:</span> "+mess+"</pre>" );
			
		}
		scrollToEl();
	  
	});
	
	socket.on("showstartbt", function(mess) {
		
		$( ".install-button" ).show();
	  
	});
	

	
	$( "#current_address" ).change(function() {
		$( "#slected_current_address" ).val($( this ).val());
	});

	$( "#current_ports" ).change(function() {
		$( "#slected_current_port" ).val($( this ).val());
	});
	$( "#mac_current_ports" ).change(function() {
		$( "#slected_mac_current_port" ).val($( this ).val());
		$( ".mac-section-address" ).show();
	});
	$( "#c_static_ip" ).change(function() {
		$( "#slected_c_static_ip" ).val($( this ).val());
		if($( this ).val() == 'static_ip'){
			$( ".firmware-search-input" ).show();			
		} else {
			$( ".firmware-search-input" ).hide();
		}
	});
	
	$('#mac_static_ip').on("change keyup paste",function() { 
	    // handle events here
	    var input_init = $( this ).val();	    
	    var val_pices = input_init.split(":");
	    if(val_pices.length != 6){
	    	
	    	input_init = input_init.replace(/ /g,'');
	    	input = input_init.replace(/:/g,'');
	    	if(input.length >= 12){
	    		input = input.substring(0,12);
	    		const first = input.substring(0,2);
				const second = input.substring(2,4);
				const third = input.substring(4,6);
				const forth = input.substring(6,8);
				const fifth = input.substring(8,10);
				const six = input.substring(10,12);

				if(input.length > 10){var output = `${first}:${second}:${third}:${forth}:${fifth}:${six}`;}
				else if(input.length > 8){var output = `${first}:${second}:${third}:${forth}:${fifth}`;}
				else if(input.length > 6){var output = `${first}:${second}:${third}:${forth}`;}
				else if(input.length > 4){var output = `${first}:${second}:${third}`;}
				else if(input.length > 2){var output = `${first}:${second}`;}
				else if(input.length > 0){var output = `${first}`;}
				
				$( this ).val(output);
	    	}
	    	
	    }
		
	});
	
});


</script>
</body> 
</html>
