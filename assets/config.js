// Load environment variables based on NODE_ENV
const path = require('path');

// Determine which .env file to load based on NODE_ENV
let envFile = '.env.development'; // default
if (process.env.NODE_ENV === 'production') {
	envFile = '.env.production';
} else if (process.env.NODE_ENV === 'staging') {
	envFile = '.env.staging';
}

require('dotenv').config({
	path: path.resolve(process.cwd(), envFile)
});

const mongoURL = process.env.MONGO_URL;
const appVersion = process.env.APP_VERSION;
const downloadurl = process.env.DOWNLOAD_URL;

module.exports = {
		mongoURL: mongoURL,
		appVersion: appVersion,
		downloadurl: downloadurl
};