html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
}

/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}

body {
  line-height: 1;
}

ol,
ul {
  list-style: none;
}

blockquote,
q {
  quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: '';
  content: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

* {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}

html {
  min-height: 100vh;
  height: auto !important;
}

html,body {
	position: relative;
}

.bg_theme {
  padding-top: 0px;
  padding-bottom: 0px;
  width: 100%;
  height: 100%;
  min-height: 100vh;
  font-family: 'Muli', sans-serif;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
          justify-content: space-between;
  background-repeat: no-repeat;
}


.bg_theme_arra {
	background: #3c3d43 url("background-light-arra.png") no-repeat bottom center;
	width: 100% !important;
	height: 100% !important;
}

.header {
  text-align: center;
	margin-top: 35px;
}

.header .logo {
  display: block;
	margin-bottom: 20px;
}

.header .logo img {
  max-width: 300px;
}

.header p {
	font-size: 12px;
	letter-spacing: -0.5px;
	line-height: 1.3;
	color: #fff;
	font-family: Verdana, "sans-serif";
	margin-top: 10px;
}

@media screen and (max-width: 700px) {
}

.main {
   text-align: center;
	flex-grow: 1;
}

.main .modal-button {
padding:10px 0 30px 0;
}

.main .search-section{
			
		}


.main #toggle-modal {
  text-decoration: none;
  font-size: 22px;
  color: #ffffff;
  text-transform: uppercase;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
	width: 270px;
	height: 50px;
	background: #000000;
	border-radius: 50px;
	line-height: 50px;
	text-align: center;
  font-family: Verdana, "sans-serif";
	display: inline-block;
}

.main #toggle-modal:hover {
  background: #0054a6;
}


.main .wifi {
  max-width: 150px;
}


@media screen and (max-width: 768px) {
	.main {
		display: flex;
		flex-wrap: wrap;
		justify-content: center;
		align-content: center;
	}
	
	.main .search-section, .wifi-img {
		width: 100%;
	}
	
	.wifi-img {
		margin-top: 20px !important;
	}
	
	.main .search-section {
		margin-top: 10px;
	}
	
	.main {
		align-content: flex-start
	}

}


.main .error-message {
  max-width: 300px;
  font-size: 22px;
  margin-left: 20px;
  margin-right: 20px;
  color: #ffffff;
  padding: 10px 15px;
  background-color: #ff000d;
  border: 1px solid #d8000c;
  border-radius: 5px;
  font-weight: bold;
  -webkit-box-shadow: 0px 10px 35px 10px black;
          box-shadow: 0px 10px 35px 10px black;
  margin-bottom: 30px;
}


.main .search-section {
  display:block;
  text-align: center;
  margin: 0;
  padding:0 15px;
}

.main .search-section form {
  position: relative;
}


.footer {
	border-top: 1px solid rgba(255,255,255,0.1);
	padding: 10px 0;
}


.footer .links ul {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  color: #ffffff;
  font-size: 13px;
}

@media screen and (max-width: 700px) {
  .footer .links ul {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}

.footer .links ul li {
  padding: 10px;
}

@media screen and (max-width: 700px) {
  .footer .links ul li {
    padding: 10px;
  }
}

.footer .links ul li a {
	text-decoration: none;
	color: #ffffff;
	-webkit-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	display: inline-block;
	min-width: 155px;
	line-height: 20px;
	font-size: 13px;
	border: 3px solid #fff;
	font-family:  Verdana, "sans-serif";
	border-radius: 50px;
	text-align: center;
	padding: 5px 01px;
	
}

.footer .links ul li a:hover {
  	background: #acc92f;
	border-color: #acc92f;
}

.footer .copyc {
	font-size: 12px;
	font-family:  Verdana, "sans-serif";
	color: #fff;
	text-align: center;
	margin-top: 10px;
	letter-spacing: -0.5px;
	margin-bottom: 10px;
	padding: 0 15px;
}

.ui-dialog {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
	width: 100% !important;
	height: 100% !important;
	top: 0 !important;
	left: 0 !important;
	background: #3c3d43 url("background-light-arra.png") no-repeat bottom center;
	border: none !important;
}

.ui-dialog .ui-dialog-titlebar {
  background: transparent;
  border: none;
  z-index: 3;
}

.ui-dialog .ui-button {
  border: none;
  background:none;
  right: 15px;
  top: 25px;
  outline: none;
}



.ui-widget-overlay {
  background: #000000;
}

#modal-vouchers {
  padding-left: 15px;
  padding-right: 15px;
  color: #000000;
  font-family: "Muli", Verdana, sans-serif;
	justify-content: center;
	flex-wrap: wrap;
	align-items: stretch;
	flex-direction: column;
	min-height: calc(100% - 50px) !important;
	display: none;
	padding-top: 0;
	padding-bottom: 0;
}

.ui-dialog #modal-vouchers {
	display: flex !important;
	flex-wrap: nowrap;
	justify-content: space-between;
}


@media screen and (max-width: 550px) {
  #modal-vouchers {
    padding-left: 0px;
    padding-right: 0px;
  }
	
	
}

.modal-container {
	width: 100%;
	flex-grow: 1;
}

.modal-header {
	text-align: center;
	margin-bottom: 10px;
}

.modal-header a {
	outline: none !important;
}

.modal-footer {
	margin-top: 20px;
	margin-bottom: 0px;
	flex-shrink: 0;
	width: 100%;
}

.modal-footer .copyc {
	font-size: 12px;
	font-family:  Verdana, "sans-serif";
	color: #fff;
	text-align: center;
	margin-top: 10px;
	letter-spacing: -0.5px;
	margin-bottom: 10px;
	padding: 0 15px;
}

.modal-vouchers__title {
  text-align: center;
  font-size: 36px;
  letter-spacing: -1px;
  color: #fff;
}

@media screen and (max-width: 550px) {
  .modal-vouchers__title {
    font-size: 28px;
  }
}

.modal-vouchers__promo {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-bottom: 70px;
  margin-top: 50px;
}

@media screen and (max-width: 550px) {
  .modal-vouchers__promo {
    margin-bottom: 30px;
    margin-top: 30px;
  }
}

.modal-vouchers__promo img {
  max-width: 100px;
  height: auto;
}

@media screen and (max-width: 550px) {
  .modal-vouchers__promo img {
    max-width: 80px;
  }
}

.modal-vouchers__promo p {
  margin-left: 10px;
  text-align: center;
  font-size: 25px;
  max-width: 380px;
	padding: 15px;
	color: #fff;
	border: 3px solid #fff;
	border-radius: 8px;
	
}

@media screen and (max-width: 550px) {
  .modal-vouchers__promo p {
    font-size: 18px;
    max-width: 330px;
  }
}

.modal-vouchers__payment {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-align: stretch;
  -webkit-align-items: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-bottom: 10px;
}

.modal-vouchers__img {
  border-radius: 10px;
  width: 80px;
  height: 45px;
  border: 2px solid #acacac;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-right: 10px;
}

@media screen and (max-width: 550px) {
  .modal-vouchers__img {
    width: 60px;
    height: 50px;
  }
}

.modal-vouchers__img:last-of-type {
  margin-right: 0;
}

.modal-vouchers__img img {
  max-width: 63px;
  max-height: 29px;
}

@media screen and (max-width: 550px) {
  .modal-vouchers__img img {
    padding: 5px;
    max-width: 100%;
  }
}

.modal-vouchers__text {
  text-align: center;
  font-size: 16px;
  color: grey;
}

@media screen and (max-width: 550px) {
  .modal-vouchers__text {
    font-size: 14px;
  }
}




body.modal-open {
overflow:hidden !important;	
}

.fbError {
width:80% !important;
margin: 20px auto 30px auto !important;
padding:10px 15px;
font-size:20px;
border-radius:5px;
box-shadow: 0px 5px 18px 10px rgba(0,0,0,0.15)
}


.fbSuccess {
width:300px !important;
margin: 20px auto 30px auto !important;
padding:15px 20px;
font-size:40px;
border-radius:5px;
box-shadow: 0px 5px 18px 10px rgba(0,0,0,0.15);
font-family: 'PT Sans',Tahoma;
color:#fff;
background:#27ae60;
text-align: center;
box-sizing: border-box;
line-height: 1;
}

a:link {
  color: #fff;
}

/* visited link */
a:visited {
  color: #fff;
}

/* mouse over link */
a:hover {
  color: #fff;
}

/* selected link */
a:active {
  color: #fff;
}


.fbNoConn {
width:300px !important;
margin: 20px auto 30px auto !important;
padding:15px 20px;
font-size:32px;
border-radius:5px;
box-shadow: 0px 5px 18px 10px rgba(0,0,0,0.15);
font-family: 'PT Sans',Tahoma;
color:#fff;
background:#a19d9d;
text-align: center;
box-sizing: border-box;
line-height: 1;
}




.err-modal-container * {
	box-sizing: border-box;
}

.err-modal-container {
width:100%;
height:100%;
position:absolute;
left:0;
top:0;
z-index: 999999;
display:none;
}

.err-modal-container .err-modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:rgba(0,0,0,0.3);
    z-index:1;
}

.err-modal-container .err-modal-content {
box-shadow: 0px 10px 35px 10px #000000;
border-radius: 20px;
background:#fff;
padding:25px;
width:520px;
position:absolute;
z-index:2;
top:50%;
left:50%;
transform:translate(-50%,-50%);
}


.err-modal-container .err-modal-content p {
    text-align: center;
    font-size: 22px;
	font-weight: bold;
	padding: 20px 0;
}

.err-modal-container .err-modal-content .err-modal-close {
padding:20px 0 0 0;
text-align:center;
}

.err-modal-container .err-modal-content .err-modal-close a {
	display:inline-block;
    font-size: 16px;
    font-family: 'PT Sans',Tahoma;
    background: #27ae60;
    box-shadow: none;
    text-shadow: none;
    color: #fff;
	padding:17px 30px;
	font-weight:bold;
	text-decoration: none;
	border-radius: 6px;
}

.err-modal-container .err-modal-content .err-modal-close a:hover {
	background: #16964C;
}

.fbError,.fbInfo{display:none;}

.wifi-img {
	margin-top: 40px;
	margin-bottom: 20px;
}

.wifi-img img {
	max-width: 100%;
	height: auto;
}

@media screen and (max-width: 1440px) {
	.wifi-img img {
	max-width: 550px;
	height: auto;
	}

}

@media screen and (max-width: 992px) {
.wifi-img img {
	max-width: 300px;
	height: auto;
	}
}

.search-section {
	color: #fff;
	text-align: center;
}

.search-section h2 {
	font-size: 23px;
	font-family: Verdana, "sans-serif";
	letter-spacing: -0.4px;
	
}

.search-section .search-input {
	width: 270px;
	height: 50px;
	margin: 0 auto;
	border: 5px solid #fff;
	position: relative;
	overflow: hidden;
	border-radius: 50px;
	margin-top: 0px;
}

.search-section .search-input input {
	width: 230px;
	height: 40px;
	outline: none;
	border: none;
	background: none;
	font-size: 25px;
	font-family: Verdana, "sans-serif";
	letter-spacing: -0.4px;
	color: #fff;
	line-height: 40px;
	text-align: center;
}


.search-section .search-input input::-webkit-input-placeholder { /* Chrome/Opera/Safari */
  color: #fff;
	opacity: 1;
}
.search-section .search-input input::-moz-placeholder { /* Firefox 19+ */
  color: #fff;
	opacity: 1;
}
.search-section .search-input input:-ms-input-placeholder { /* IE 10+ */
  color: #fff;
	opacity: 1;
}
.search-section .search-input input:-moz-placeholder { /* Firefox 18- */
  color: #fff;
	opacity: 1;
}

.search-section .search-input button {
	position: absolute;
	right: 10px;
	top: 11px;
	width: 11px;
	height: 18px;
	background: none;
	border: none;
	outline: none;
	cursor: pointer;
	padding: 0;
	margin: 0;
}

.rec-error {
	background:#60352f;
	width: 270px;
	height: 58px;
	outline: none;
	border: none;
	font-size: 25px;
	font-family: Verdana, "sans-serif";
	letter-spacing: -0.4px;
	color:#fff;
	padding: 15px;
	line-height: 22px;
	text-align: center;
	display: inline-block;
	border-radius: 50px;
	cursor: default;
	border: 3px solid#e41401;
	margin-top: 10px;
}

.errors-container {
	background:#60352f;
	min-width: 480px;
	max-width: 480px;
	outline: none;
	border: none;
	font-size: 17px;
	font-family: Verdana, "sans-serif";
	font-weight: 400;
	letter-spacing: -0.4px;
	color:#fff;
	padding: 15px;
	line-height: 24px;
	text-align: center;
	border-radius: 5px;
	cursor: default;
	border: 2px solid #e41401;
	display: block;
	margin: 20px auto 10px auto;
}

@media screen and (max-width: 540px) {
.errors-container {
	max-width: 100%;
	}
}

#modal-vouchers-payment.ui-dialog-content {
  color: #000000;
  font-family: "Muli", Verdana, sans-serif;
	display: flex;
	justify-content: center;
	flex-wrap: wrap;
	align-items: stretch;
	min-height: 100vh !important;
	overflow: auto;
	padding: 20px;
	border: none;
	margin-top: -30px;
}


@media screen and (max-width: 992px) {
	#modal-vouchers-payment.ui-dialog-content  {
		padding: 20px 0;
		min-height: 100% !important;
	}
	
	#modal-vouchers-payment iframe {
		padding: 20px 5px 0 5px;
	}
}

#modal-vouchers-payment .voucher-iframe-header {
	width: 100%;
	text-align: center;
}

#modal-vouchers-payment .voucher-iframe-header img {
	width: 125px;
	height: auto;
}

#modal-vouchers-payment iframe {
	height: calc(100% - 50px);
	overflow: auto;
	padding: 10px 5px;
}

.success-mess{
	background:
#759968;

border: 3px solid
#8ab989;
	
	width: 270px;

height: 58px;

outline: none;

border: none;

font-size: 25px;

font-family: Verdana, "sans-serif";

letter-spacing: -0.4px;

color:
#fff;

padding: 15px;

line-height: 22px;

text-align: center;

display: inline-block;

border-radius: 50px;

cursor: default;

margin-top: 10px;
}

.input-preloader {
position: absolute;
right: 6px;
top: 4px;
width: 32px;
height: 32px;
padding: 0;
margin: 0;
z-index: 2;
}

.nv-error {
    background: #60352f;
    color: #fff;
    width: 380px;
    padding: 15px;
    border: 3px solid #e41401;
    margin-top: 20px;
    border-radius: 12px;
    text-align: center;
    line-height: 1.5;
    font-size: 18px;
    font-weight: 700;
    box-shadow: 0 0 10px 0 rgba(0,0,0,0.3);
}

.ar-custom-select {
  width: 270px;
  position: relative;
  box-sizing: border-box;
	margin:0 auto;
	display: inline-block;
}

.ar-custom-select select {
  width: 270px;
  height: 50px;
  outline: none;
  border: none;
  background: none;
  font-size: 14px;
  font-family: Verdana, "sans-serif";
  font-weight: 700;
  letter-spacing: -0.4px;
  color: #fff;
  line-height: 40px;
  border: 3px solid #fff;
  border-radius: 50px;
  padding: 0px 35px 0px 15px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  box-sizing: border-box;
	
}

.ar-custom-select select option {
  color: #000;
  font-family: Verdana, "sans-serif";
  font-weight: 700;
  padding: 5px 10px;
}

.ar-custom-select:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 #000;
}

.ar-custom-select&::-ms-expand {
  display: none;
}

.ar-custom-select:after {
  position: absolute;
  content: "";
  top: 23px;
  right: 15px;
  width: 0;
  height: 0;
  border: 6px solid transparent;
  border-color: #fff transparent transparent transparent;
}
.mess{
	width: 50%;
    margin: 0 auto;
    text-align: left !important;
	margin-bottom: 30px;
	height: 450px;
	overflow-y: auto;
}

.back-button{
	border: solid 1px !important;
	background: transparent !important;
}
.back-button:hover{
	background: #0000 !important;
}
.mac-search-input{
	width: 542px !important;
}
.mac-search-input-text{
	width: 480px !important;
	font-size: 20px !important;
}

