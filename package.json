{"name": "app", "version": "1.0.0", "description": "test", "main": "app.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build:dev": "cross-env NODE_ENV=development pkg --targets node14-win-x64 . --output loadFirmware-dev.exe", "build:staging": "cross-env NODE_ENV=staging pkg --targets node14-win-x64 . --output loadFirmware-staging.exe", "build:prod": "cross-env NODE_ENV=production pkg --targets node14-win-x64 . --output loadFirmware-prod.exe", "build": "echo \"Please specify environment: npm run build:dev, npm run build:staging, or npm run build:prod\""}, "author": "", "license": "ISC", "pkg": {"assets": ["assets/**/*"], "builds": ["builds/**/*"]}, "bin": {"app": "./app.js"}, "dependencies": {"@serialport/parser-readline": "^13.0.0", "dotenv": "^16.5.0", "enquirer": "^2.4.1", "express": "^4.18.2", "mongodb": "^3.7.4", "node-html-parser": "^1.4.9", "openurl": "^1.1.1", "os": "^0.1.2", "prompt-list": "^3.2.0", "serialport": "^13.0.0", "socket.io": "^2.5.0", "systeminformation": "^5.25.11", "tftp": "^0.1.2"}, "devDependencies": {"cross-env": "^7.0.3"}}