{"name": "app", "version": "1.0.0", "description": "test", "main": "app.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "pkg": {"assets": ["assets/**/*"], "builds": ["builds/**/*"]}, "bin": {"app": "./app.js"}, "dependencies": {"@serialport/parser-readline": "^13.0.0", "enquirer": "^2.4.1", "express": "^4.18.2", "mongodb": "^3.7.4", "node-html-parser": "^1.4.9", "openurl": "^1.1.1", "os": "^0.1.2", "prompt-list": "^3.2.0", "serialport": "^13.0.0", "socket.io": "^2.5.0", "systeminformation": "^5.25.11", "tftp": "^0.1.2"}}