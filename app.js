// writen by a guy who has never used Javascript, and does not care to.
//
// TO BUILD EXE FOR WINDOWS, FROM A WINDOWS PC:
// only needed to do this once:
//Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope CurrentUser
// then every time:
//pkg app.js --output loadFirmware.exe

/*

High priority
TODO: Self update.  Do not allow older version to run.  Verify the current version is approved (currently done by mongo query).  update to latest if needed (not implemented yet).
TODO:  make binary TFTP images part of the EXE.  A clever user could copy TFTP images in the TFTP directory and circumvent version checking.  Prevent this by making them part of the EXE and not accessable to the end user.  The tftp library allows serving data from a variable.
TODO: implement stricter version control.  A build system should fail (or not make a production version) if there are changes from git.  use git tag or git commit as version.
TODO: Log failures in the mongodb - maybe in a separate table.  Need all serial communication and some detail about the error.  Currently exceptions just kill the exe.

Medium priority:
TODO: Add tests.  More details will be provided but basically commands with an exected response.  This list will continue to grow.  like:
  Verify the size of RAM, NOR (SPI flash), and NAND.
  Verify the 882 radio is responding.
  Verify the mac addresses are correct.
TODO: let the user see the console data as it happens.  Maybe some kind of split screen?  Bottom line says what it is doing, the rest scrolls with output.

Low priority:
TODO: automate configuration of ethernet adapter for TFTP.  Let the user select the adapter similar to the com port.  set the hard coded ip.  return settings on exit.
TODO: loop and allow exit.  currently the exe just tries to program one board.  at the end the user must hit ctrl-c to exit.
TODO: include instructions or links for disabling firewall.
TODO: rename from hello to "?"
*/

// U-Boot prompt constant - can be either '(IPQ40xx) #' or 'NHX53X2-V03-244-NPN-512M16-D4#'
const UBOOT_PROMPT = 'NHX53X2-V03-244-NPN-512M16-D4#';
const LINUX_PROMPT = 'root@NHX53X2-V03-244-NPN-512M16-D4-64:/#';

/*

To test the firmware load:
    1. This step not needed right now - all ip addresses are allowed.  keepiong it on the list for future reference. Find your internet ipv4 address so Yancy can white list it.  Use a site like:https://www.whatismyip.com/.  If that site does not give your ipv4 address google around for another “what is my ipv4” site.
    2. assign an ip address of ************ to the USB to ethernet adapter.  Google for your version of windows.
    3. Download contents from the shared router drive under ArraRouter\YancyTest\firmwareLoad.zip
    4. unzip and run firmwareLoad.exe.  do this from a command line or elses the window may disappear on errror.

*/

var utility = require('./assets/utility');
const { Select } = require('enquirer')

const { SerialPort } = require('serialport')
const { DelimiterParser } = require('@serialport/parser-delimiter')
//const InterByteTimeout = require('@serialport/parser-inter-byte-timeout')
//const InterByteTimeout = SerialPort.parsers.interByteParser
const { ReadlineParser } = require('@serialport/parser-readline')
const PromptList = require('prompt-list')
const tftp = require('tftp')

var Binary = require('mongodb').Binary;

var MongoClient = require('mongodb').MongoClient;

const mongoURL = utility.mongoURL;
const appVersion = utility.appVersion;
const askForPort = async () => {
	const ports = await SerialPort.list()
	 if (ports.length === 0) {
	   console.error('No ports detected and none specified')
	   process.exit(2)
	 }

	const answer = await new Select({
	   name: 'serial-port-selection',
	   message: 'Select a serial port to open',
	   choices: ports.map((port, i) => ({
	     value: `[${i + 1}]\t${port.path}\t${port.pnpId || ''}\t${port.manufacturer || ''}`,
	     name: port.path,
	   })),
	   required: true,
	 }).run()
	 return answer
}
var globalPorts = [];
const getPorts = async () => {
	const ports = await SerialPort.list()
	var html = '<option value="" disabled selected>Please select a Port</option>';
	var counter = 0;


	for (const [single_key, port] of Object.entries(ports)) {
//			console.log('port',port);
	  	  counter++;
	  	  globalPorts[counter] = port.path;
	  	  var val = counter;
//	  	  console.log(val);
//	  	  html += '<option value="'+val+'">'+port.path+'</option>';
	  	  html += '<option value="'+val+'">'+port.path+'</option>';
	}
	// test port
//	html += '<option value="COM3">COM3</option>';
	return html;
}

const askForPortAsync = async () =>  {
 return SerialPort.list().then(ports => {
  if (ports.length === 0) {
   console.error('No ports detected and none specified')
   process.exit(2)
  }

  const portSelection = new PromptList({
   name: 'serial-port-selection',
   message: 'Select a serial port to open',
   choices: ports.map((port, i) => ({
    value: `[${i + 1}]\t${port.path}\t${port.pnpId || ''}\t${port.manufacturer || ''}`,
    name: port.path,
   })),
   validate: Boolean, // ensure we picked something
  })

  return portSelection.run().then(answer => {
   console.log(`Opening serial port: ${answer}`)
   return answer
  })
 })
}

var http = require('http');
var fs = require('fs');
var path = require('path');
var io = require('socket.io')(http);
var folder_filePath = path.join(__dirname, 'assets');

function startFirmwareTFTPServer(socket,current_ip,portPath_key,static_ip_type,static_ip_address){

	   var server = tftp.createServer ({
		  //host: "*************",
		  //host: "0.0.0.0",
//		  host: "************",
		  host: current_ip,
		  port: 69,
		  root: folder_filePath + "/tftp",
			denyPUT: true
		});

		server.on ("error", function (error){
			//Errors from the main socket
			console.error (error);
		});

		server.on ("request", function (req){
			req.on ("error", function (error){
				//Error from the request
				console.error (error);
			});
		});

		server.on ("listening", function (error){
			//Error from the request
			console.log("TFTP Server is Running");
			startAppProccess(socket,current_ip,portPath_key,static_ip_type,static_ip_address);

		});

		server.listen ();

}

function startAppProccess(socket,current_ip,portPath_key,static_ip_type,static_ip_address){



	   const run = async () => {
		   try{
		    var dbRecord = {};


		    console.log("IP Address:" +current_ip);
//		    console.log("portPath_key:" +portPath_key);
//		    console.log("static_ip_type:" +static_ip_type);
//		    console.log("static_ip_address:" +static_ip_address);
		    console.log("verifying this version is approved.");
		    console.log("bef verifyVersion");
		    var versionOK = await utility.verifyVersion(appVersion);
		    console.log("aft verifyVersion", versionOK);

		    var verifyLatestV = await utility.verifyLatestVersion(appVersion);
		    console.log("aft verifyLatestVersion", verifyLatestV);

		    if (!versionOK){
				console.log("THIS VERSION IS NOT APPROVED.  EXITING");
				process.exit(1);
		    }else if(!verifyLatestV){
		    	process.exit(1);
		    } else {


		      // console.log("inserting Results");
		      // dbRecord.date = new Date();
		      // await dbInsert(dbRecord);
		      // console.log("results Inserted");



		      // currently just returns "Gxxxxxxxxxxxx"
		      var PCSerial = await utility.getPCSerial();
		      console.log("PCSerial %s",PCSerial);

		       //const portPath = await askForPortAsync();
//		       const portPath = await askForPort();
//		       const portPath = 'COM3';
		       portPath = globalPorts[portPath_key];
		       console.log("");
		       console.log(portPath);
		       console.log("");

//NEW: moved where port is in this call.  please verify it is ok and I got all of them.
		       const port = new SerialPort( {
				   path: portPath,
		           baudRate: 115200,
		           dataBits: 8,
		           parity: 'none',
		           stopBits: 1,
		           flowControl: false,
		           usePromises: true,
		       }, function(err) {
		           if (err){
		               console.log('error: ', err.message)
		               port.close()
		           } else {

		           }

		        });

		        var allSerialData;
		        allSerialData = undefined;
		        delete(allSerialData);

		        port.on('data', function (data) {
		           //console.log('Data: %s', data)
		           allSerialData += data;
		        })

		        var recdata;


		        console.log("power router on... waiting for \"Hit any key to stop autoboot:\"");
		        //wait  for - Hit any key to stop autoboot:
		        recdata = await utility.serialCommand(port," ",'Hit any key to stop autoboot:',10000000, 500);

		        console.log("recdata %s",recdata)

		        console.log("detected router");
		        //uboot is not really ready when it says hit any key.  pasue a bit
		        await new Promise(resolve => setTimeout(resolve, 1000));
		        recdata = await utility.serialCommand(port,  " ", UBOOT_PROMPT,5000,500);
		        console.log("recdata %s",recdata)

		        //port.flush(function(err,results){});

		        console.log("at uboot prompt");

		        if(static_ip_type == 'static_ip'){
		        	console.log("static ip address:" +static_ip_address);
		        	recdata = await utility.serialCommand(port,  "setenv ipaddr "+static_ip_address+" && setenv serverip "+current_ip+"\n", UBOOT_PROMPT,500,500);
		        } else {
		        	console.log("no static ip address, using dhcp");
		        	recdata = await utility.serialDynamicCommand(port,  "setenv autoload no && dhcp && setenv serverip "+current_ip+"\n", UBOOT_PROMPT,500,500);
		        }

//		        var testfile = await utility.serialCommand(port,  "tftpboot 0x84000000 testFile.txt \n", '(IPQ40xx) #',90000,500);
//		        console.log("testfile: " + testfile);
//		        process.exit(1);

		        await new Promise(resolve => setTimeout(resolve, 5000));


		        console.log("-----------------------------------------------------------------------");
		        console.log("updating Kernel and filesystem");

		        recdata = await utility.serialCommand(port,
		                                      "tftpboot NHX53X2-V03-244-NPN-512M16-D4-64-ubi-root.img && nand erase.chip && nand write ${fileaddr} 0xc0000 ${filesize} && reset \n",
		                                      'Starting kernel ...',
		                                       90000,
		                                       500);


		        console.log("aft updating Kernel and filesystem recdata",recdata)

		        console.log("waiting for first boot");

		        // get the reboot after config
		        recdata = await utility.serialCommand(port,
		          "\n",
		          'Starting kernel ...',
		          100000,
		          500);

		        // wait 70 seconds for boot
		        console.log("Waiting for reboot to happen");
		        await new Promise(resolve => setTimeout(resolve, 70000));
		        console.log("Done rebooting");

		        console.log("starting console");
		        recdata = await utility.serialCommand(port,"\n",'login:',1000, 500);
		        console.log("sending user");
		        recdata = await utility.serialCommand(port,"root\n",'Password:',1000, 500);
		        console.log("sending credentials");
		        recdata = await utility.serialCommand(port,"console123\n",'root@',1000, 500);

		        console.log("console started");

		        recdata = await utility.serialCommand(port,"export PS1=prompt:\n",'prompt:',1000, 500);

		        console.log("setting up VPN");
		        recdata = await utility.serialCommand(port,"arra-support-setup\n",'prompt:',1000, 500);
		        // the last 3 lines should be the VPN config.
		        var VPNconfig = String(recdata).split(/[\r\n]+/).slice(-4,-1).join('\n');  //no validation done here but it needs it!
		        console.log("vpn config: %s",VPNconfig);

		        //console.log(allSerialData);

		        // get serial number
		        console.log("get serial number");
		        recdata = await utility.serialCommand(port,"ip link show br-lan | grep link/ether | awk '{print $2}' | tr 'a-z' 'A-Z'\n",'prompt:',1000, 500);
		        var routerSerialNumber = String(recdata).split(/[\r\n]+/)[1];  //no validation done here but it needs it!
		        console.log("router serial %s",routerSerialNumber);


		        //SERIALNUM=$(ip link show eth0 | grep link/ether | awk '{print $2}' | tr 'a-z' 'A-Z'); IP6START=FDE3:8675:3090;echo -n $IP6START":0000:0000:";echo $SERIALNUM | awk -F":" '{print $1$2":"$3$4":"$5$6}'

		        // get vpn address
		        console.log("get VPN ipv6 address");
		        recdata = await utility.serialCommand(port,"SERIALNUM=$(ip link show br-lan | grep link/ether | awk '{print $2}' | tr 'a-z' 'A-Z'); IP6START=FDE3:8675:3090;echo -n $IP6START\":0000:0000:\";echo $SERIALNUM | awk -F\":\" '{print $1$2\":\"$3$4\":\"$5$6}'\n",'prompt:',1000, 500);
		        var routerVPN_IPv6Address = String(recdata).split(/[\r\n]+/).slice(-2,-1).join('\n');  //no validation done here but it needs it!
		        console.log(" router VPN_IPv6Address %s", routerVPN_IPv6Address);


//NEW: might need a port.close() here.  see comment in macAppProccess.


		        // Would be nice to also add a machine name, local machine serial number from processor? something
		        // to identify who / where the board is being programmed from.
		        dbRecord.date = new Date();
		        dbRecord.routerSerialNumber = routerSerialNumber;
		        dbRecord.PCSerialNumber = PCSerial;
		        dbRecord.VPN_IPv6Address = routerVPN_IPv6Address;
		        dbRecord.VPNconfig = VPNconfig;
		        dbRecord.allSerialData = allSerialData;
		        dbRecord.allSerialDataBinary = Binary(allSerialData);
		        await utility.dbInsert(dbRecord);

		        socket.emit('showfirmaredashboard');
	    		return false;
		      }


		    } catch (err){
		    	console.error(err);
				utility.writemongodberr('Run system',err);
			}

		  }

		  run().catch(error => {
		    console.error(error)
		    process.exit(1)
		  })


		//TFTPServer.listen();
		//listPorts();

		// console.log("");
		// const portPath = askForPort();
		// console.log(portPath);
		// console.log("");


		//run();





}

function startMacAppProccess(socket,portPath_key,first_mac,second_mac,third_mac,fourth_mac){

	   const run = async () => {
		   try{

		    var dbRecord = {};
// NEW: change this to 8 mac addresses in sequence.  We only set one mac address in the router and it increments to get the next 7.
// We need to know all 8 mac addresses for logging and tracking.  Do we pass 8 in or increment in this code?
			console.log("Init proccess for MAC Address: " +first_mac); 
			console.log("Init proccess for MAC Address: " +second_mac);
			console.log("Init proccess for MAC Address: " +third_mac);
			console.log("Init proccess for MAC Address: " +fourth_mac);
//		    console.log("portPath_key:" +portPath_key);
//		    console.log("static_ip_type:" +static_ip_type);
//		    console.log("static_ip_address:" +static_ip_address);
		    console.log("verifying this version is approved.");
		    console.log("bef verifyVersion");
//NEW:only commented this out for testing.
		    // var versionOK = await utility.verifyVersion(appVersion);
		    // console.log("aft verifyVersion", versionOK);
		    // var verifyLatestV = await utility.verifyLatestVersion(appVersion);
		    // console.log("aft verifyLatestVersion", verifyLatestV);

		    // if (!versionOK){
			// 	console.log("THIS VERSION IS NOT APPROVED.  EXITING");
			// 	process.exit(1);
		    // }else if(!verifyLatestV){
		    // 	process.exit(1);
		    // } else {

//		      var verifyMacAddressValFirst = await utility.verifyMacAddress(first_mac);
//			  if (!verifyMacAddressValFirst){
//				    socket.emit('showmacdashboard');
//		    		return false;
//			  }


		      var PCSerial = await utility.getPCSerial();
		      console.log("PCSerial %s",PCSerial);

		       //const portPath = await askForPortAsync();
//		       const portPath = await askForPort();
//		       const portPath = 'COM3';
		       portPath = globalPorts[portPath_key];
		       console.log("");
		       console.log(portPath);
		       console.log("");

//NEW: moved where port is in this call.  The API changed.  Please verify it is ok and I got all of them.
		       const port = new SerialPort( {
				   path: portPath,
		           baudRate: 115200,
		           dataBits: 8,
		           parity: 'none',
		           stopBits: 1,
		           flowControl: false,
		           usePromises: true,
		       }, function(err) {
		           if (err){
		               console.log('error: ', err.message)
		               port.close()
		           } else {

		           }

		        });

		        var allSerialData;
		        allSerialData = undefined;
		        delete(allSerialData);

		        port.on('data', function (data) {
		           //console.log('Data: %s', data)
		           allSerialData += data;
		        })

		        var recdata;


		        console.log("power router on... waiting for \"Hit any key to stop autoboot:\"");
		        //wait  for - Hit any key to stop autoboot:
		        recdata = await utility.serialCommand(port," ",'Hit any key to stop autoboot:',10000000, 500);

		        console.log("recdata %s",recdata)

		        console.log("detected router");

				//wait 70 seconds for boot
				console.log("********************************************************************************");
				console.log("Waiting 70 seconds for device to boot.");
				console.log("Ignore log message above: eth0 MAC Address from ART is not valid");
				console.log("********************************************************************************");
				await new Promise(resolve => setTimeout(resolve, 70000));
				console.log("Done rebooting");

				recdata = await utility.serialCommand(port,  "\n", LINUX_PROMPT, 1000, 500);
		        console.log("recdata %s",recdata);

				//Sample output from commands:
				// root@NHX53X2-V03-244-NPN-512M16-D4-64:/# nvram set nhxmac=00:66:88:00:00:00
				// root@NHX53X2-V03-244-NPN-512M16-D4-64:/# nvram get nhxmac
				// 00:66:88:00:00:00


				recdata = await utility.serialCommand(port,  "nvram get nhxmac\n", LINUX_PROMPT, 1000, 500);
				console.log("recdata '%s'",recdata);

			//recdata now has the mac addresses if it was previously set.  If not set recdata will be empty.
// NEW: if it was set, it should be recorded in mongodb.

				recdata = await utility.serialCommand(port,  "nvram set nhxmac=" + first_mac + "\n", LINUX_PROMPT, 5000, 500);
				console.log("recdata %s",recdata);

				recdata = await utility.serialCommand(port,  "nvram commit\n", LINUX_PROMPT, 5000, 500);
				console.log("recdata %s",recdata);
//NEW: validate recdata is blank here, no errror messages.

				//verify mac address was set
				recdata = await utility.serialCommand(port,  "nvram get nhxmac\n", LINUX_PROMPT, 1000, 500);
				console.log("recdata '%s'",recdata);
// NEW: Verify the correct mac address is in recdata.

//NEW: interact with the mongoDB to send results and mac addres(s).  This may be little different because of the 8 mac addresses.
// should also verify with isARRAMacAddress()

//NEW: it seems to leave the com port open.  Maybe this is a change in the library, or in some code I have commented out.  
// without this close when it is done if I hit the back button and try to load another MAC address it will error.  Restart the app and it is fine.
// check that other opens are closed properly.
port.close();

// NEW: commented out the logic for the mesh router mac addresses below.  This can be removed after comparing to the above logic.
// 		        //uboot is not really ready when it says hit any key.  pasue a bit
// 		        await new Promise(resolve => setTimeout(resolve, 1000));
// 		        recdata = await utility.serialCommand(port,  " ", UBOOT_PROMPT,5000,500);
// 		        console.log("recdata %s",recdata)

// 		        //port.flush(function(err,results){});

// 		        console.log("at uboot prompt");

// 		        // start indentify device mac
// 		        recdata = await utility.serialCommand(port,  "sf probe; sf read 0x84000000 0x170000 0x10000\n", UBOOT_PROMPT,500,500);
// 		        console.log("recdata %s",recdata)

// 		        console.log("reading MAC address");

// 		        recdata = await utility.serialCommand(port,  "md.b 0x84000000 6;md.b 0x84000006 6;md.b 0x84001006 6;md.b 0x84005006 6\n", UBOOT_PROMPT,500,500);
// 				// end indentify device mac

// 		        // sample output:
// 		        //84000000: f0 aa 0b 00 00 01    ......
// 		        //84000006: f0 aa 0b 00 00 02    ......
// 		        //84001006: f0 aa 0b 00 00 03    ......
// 		        //84005006: f0 aa 0b 00 00 04    ......

// 		        console.log("MAC addresses recdata %s",recdata)


// //				var eth0MAC   = "F0:AA:0B:00:20:08"
// //		        var eth1MAC   = "F0:AA:0B:00:20:09"
// //		        var w24ghzMAC = "F0:AA:0B:00:20:0A"
// //		        var w5ghzMAC  = "F0:AA:0B:00:20:0B"

// 		        var eth0MAC   = utility.parseMAC(String(recdata).split(/[\r\n]+/)[1])
// 		        var eth1MAC   = utility.parseMAC(String(recdata).split(/[\r\n]+/)[2])
// 		        var w24ghzMAC = utility.parseMAC(String(recdata).split(/[\r\n]+/)[3])
// 		        var w5ghzMAC  = utility.parseMAC(String(recdata).split(/[\r\n]+/)[4])

// 		        // start verify if device had ARRA MAC Addresses

// //		        var eth0MAC   = "F0:AA:0B:00:20:08";

// 		    	var verifyeth0MAC = await utility.isARRAMacAddress(eth0MAC,"eth0MAC");
// 		    	if (!verifyeth0MAC){
// 		    		socket.emit('showmacdashboard');
// 		    		return false;
// 			    }
// 		    	var verifyeth1MAC = await utility.isARRAMacAddress(eth1MAC,"eth1MAC");
// 		    	if (!verifyeth1MAC){
// 		    		socket.emit('showmacdashboard');
// 		    		return false;
// 			    }
// 		    	var verifyw24ghzMAC = await utility.isARRAMacAddress(w24ghzMAC,"w24ghzMAC");
// 		    	if (!verifyw24ghzMAC){
// 		    		socket.emit('showmacdashboard');
// 		    		return false;
// 			    }
// 		    	var verifyw5ghzMAC = await utility.isARRAMacAddress(w5ghzMAC,"w5ghzMAC");
// 		    	if (!verifyw5ghzMAC){
// 		    		socket.emit('showmacdashboard');
// 		    		return false;
// 			    }

// 		        // end verify if device had ARRA MAC Addresses

// 		    	// start verify in DB new MAC Addresses
// 				  var verifyMacAddressValFirst = await utility.verifyMacAddress(first_mac);
// 				  if (!verifyMacAddressValFirst){
// 					    socket.emit('showmacdashboard');
// 			    		return false;
// 				  } else {
// 					    var ARRAeth0MAC   = first_mac;
// 				  }
// 				  var verifyMacAddressValSecond = await utility.verifyMacAddress(second_mac);
// 				  if (!verifyMacAddressValSecond){
// 					    socket.emit('showmacdashboard');
// 			    		return false;
// 				  } else {
// 					    var ARRAeth1MAC   = second_mac;
// 				  }
// 				  var verifyMacAddressValThird = await utility.verifyMacAddress(third_mac);
// 				  if (!verifyMacAddressValThird){
// 					    socket.emit('showmacdashboard');
// 			    		return false;
// 				  } else {
// 					    var ARRAw24ghzMAC   = third_mac;
// 				  }
// 				  var verifyMacAddressValFourth = await utility.verifyMacAddress(fourth_mac);
// 				  if (!verifyMacAddressValFourth){
// 					    socket.emit('showmacdashboard');
// 			    		return false;
// 				  } else {
// 					    var ARRAw5ghzMAC   = fourth_mac;
// 				  }
// 				 // end verify in DB new MAC Addresses

// 				// add new addresses in DB
// 				var dbAdressRecord1 = {};
// 				dbAdressRecord1.date = new Date();
// 				dbAdressRecord1.mac_address = first_mac.toLowerCase();
// 				dbAdressRecord1.status = "being programmed";
// 				dbAdressRecord1.address_type = "eth0MAC";
// 				dbAdressRecord1.old_mac_address = eth0MAC;
// 		        await utility.dbInsertAddresses(dbAdressRecord1);

// 		        // add new addresses in DB
// 				var dbAdressRecord2 = {};
// 				dbAdressRecord2.date = new Date();
// 				dbAdressRecord2.mac_address = second_mac.toLowerCase();
// 				dbAdressRecord2.status = "being programmed";
// 				dbAdressRecord2.address_type = "eth1MAC";
// 				dbAdressRecord2.old_mac_address = eth1MAC;
// 		        await utility.dbInsertAddresses(dbAdressRecord2);

// 		        // add new addresses in DB
// 				var dbAdressRecord3 = {};
// 				dbAdressRecord3.date = new Date();
// 				dbAdressRecord3.mac_address = third_mac.toLowerCase();
// 				dbAdressRecord3.status = "being programmed";
// 				dbAdressRecord3.address_type = "w24ghzMAC";
// 				dbAdressRecord3.old_mac_address = w24ghzMAC;
// 		        await utility.dbInsertAddresses(dbAdressRecord3);

// 		        // add new addresses in DB
// 				var dbAdressRecord4 = {};
// 				dbAdressRecord4.date = new Date();
// 				dbAdressRecord4.mac_address = fourth_mac.toLowerCase();
// 				dbAdressRecord4.status = "being programmed";
// 				dbAdressRecord4.address_type = "w5ghzMAC";
// 				dbAdressRecord4.old_mac_address = w5ghzMAC;
// 		        await utility.dbInsertAddresses(dbAdressRecord4);

// 		        // console.log("MAC 2 %s",String(recdata).split(/[\r\n]+/)[2]);  //no validation done here but it needs it!
// 		        // console.log("MAC 3 %s",String(recdata).split(/[\r\n]+/)[3]);  //no validation done here but it needs it!
// 		        // console.log("MAC 4 %s",String(recdata).split(/[\r\n]+/)[4]);  //no validation done here but it needs it!

// 		        recdata = await utility.serialCommand(port,  "md.b 0x84001002 2;md.b 0x84005002 2\n", UBOOT_PROMPT,500,500);
// 		        console.log("crcs %s",recdata)
// 		        // sample output:
// 		        //md.b 0x84001002 2;md.b 0x84005002 2
// 		        //84001002: 94 ca    ..
// 		        //84005002: 73 6c    sl

// 		        var w24ghzCRC = String(recdata).split(/[\r\n]+/)[1].split(" ")[1] + String(recdata).split(/[\r\n]+/)[1].split(" ")[2]
// 		        var w5ghzCRC  = String(recdata).split(/[\r\n]+/)[2].split(" ")[1] + String(recdata).split(/[\r\n]+/)[2].split(" ")[2]

// 		        var ARRAw24ghzCRC
// 		        var ARRAw5ghzCRC


// 		        console.log("crcs      %s %s",w24ghzCRC,w5ghzCRC)

// 		        ARRAw24ghzCRC = utility.calcXOR(w24ghzCRC,w24ghzMAC,ARRAw24ghzMAC)
// 		        ARRAw5ghzCRC = utility.calcXOR(w5ghzCRC,w5ghzMAC,ARRAw5ghzMAC)

// 		        console.log("ARRA crcs %s %s",ARRAw24ghzCRC.toString(16),ARRAw5ghzCRC.toString(16))


// 		        recdata = await utility.serialCommand(port, utility.MACWriteRam(0x84000000,ARRAeth0MAC) , UBOOT_PROMPT,3500,500);
// 		        console.log("arra eth0MAC recdata ",recdata);
// 		        recdata = await utility.serialCommand(port, utility.MACWriteRam(0x84000006,ARRAeth1MAC) , UBOOT_PROMPT,3500,500);
// 		        console.log("arra eth1MAC recdata ",recdata);
// 		        recdata = await utility.serialCommand(port, utility.MACWriteRam(0x84001006,ARRAw24ghzMAC) , UBOOT_PROMPT,3500,500);
// 		        console.log("arra w24ghzMAC recdata ",recdata);
// 		        recdata = await utility.serialCommand(port, utility.MACWriteRam(0x84005006,ARRAw5ghzMAC) , UBOOT_PROMPT,3500,500);
// 		        console.log("arra w5ghzMAC recdata ",recdata);

// 		        // write the new CRC values
// 		        recdata = await utility.serialCommand(port, "mw.b 0x84001002 0x" + ARRAw24ghzCRC.toString(16).substr(0,2) + "; mw.b 0x84001003 0x" + ARRAw24ghzCRC.toString(16).substr(2,4) + "\n" , UBOOT_PROMPT,500,500);
// 		        console.log("ARRAw24ghzCRC recdata ",recdata);
// 		        recdata = await utility.serialCommand(port, "mw.b 0x84005002 0x" + ARRAw5ghzCRC.toString(16).substr(0,2)  + "; mw.b 0x84005003 0x" + ARRAw5ghzCRC.toString(16).substr(2,4)  + "\n" , UBOOT_PROMPT,500,500);
// 		        console.log("ARRAw5ghzCRC recdata ",recdata);

// 		        // This is the only command that modifies flash.  comment this line out for testing.
// 		        recdata = await utility.serialCommand(port,  "sf erase 0x170000 0x10000;sf write 0x84000000 0x170000 0x10000\n", UBOOT_PROMPT,3500,500);
// 		        console.log("Run command that modifies flash recdata ",recdata);

// 		        console.log("Verify new addresses on device!")
// 		        // start indentify device mac
// 		        recdata = await utility.serialCommand(port,  "sf probe; sf read 0x84000000 0x170000 0x10000\n", UBOOT_PROMPT,500,500);
// 		        console.log("recdata %s",recdata)

// 		        console.log("reading New MAC address");

// 		        recdata = await utility.serialCommand(port,  "md.b 0x84000000 6;md.b 0x84000006 6;md.b 0x84001006 6;md.b 0x84005006 6\n", UBOOT_PROMPT,500,500);
// 		        console.log("recdata %s",recdata);
// 		        // end indentify device mac

// 		        // verify device new addresses
// 		        var eth0MAC_new   = utility.parseMAC(String(recdata).split(/[\r\n]+/)[1])
// 		        console.log("Device address ("+eth0MAC_new+"); Init address ("+first_mac+").");
// 		        var eth1MAC_new   = utility.parseMAC(String(recdata).split(/[\r\n]+/)[2])
// 		        console.log("Device address ("+eth1MAC_new+"); Init address ("+second_mac+").");
// 		        var w24ghzMAC_new = utility.parseMAC(String(recdata).split(/[\r\n]+/)[3])
// 		        console.log("Device address ("+w24ghzMAC_new+"); Init address ("+third_mac+").");
// 		        var w5ghzMAC_new  = utility.parseMAC(String(recdata).split(/[\r\n]+/)[4])
// 		        console.log("Device address ("+w5ghzMAC_new+"); Init address ("+fourth_mac+").");

// 		        var updateAddressStatus = await utility.verifyNewMacAddress(eth0MAC_new,first_mac,"eth0MAC");
// 		        if(!updateAddressStatus){
// 		        	console.log("eth0MAC Mac Address ("+eth0MAC_new+") on device IS NOT UPDATED.");
// 		        	socket.emit('showmacdashboard');
// 		    		return false;
// 		        } else {
// 		        	console.log("eth0MAC Mac Address ("+eth0MAC_new+") on device IS UPDATED.");
// 		        }
// 		        var updateAddressStatus = await utility.verifyNewMacAddress(eth1MAC_new,second_mac,"eth1MAC");
// 		        if(!updateAddressStatus){
// 		        	console.log("eth1MAC Mac Address ("+eth1MAC_new+") on device IS NOT UPDATED.");
// 		        	socket.emit('showmacdashboard');
// 		    		return false;
// 		        } else {
// 		        	console.log("eth1MAC Mac Address ("+eth1MAC_new+") on device IS UPDATED.");
// 		        }
// 		        var updateAddressStatus = await utility.verifyNewMacAddress(w24ghzMAC_new,third_mac,"w24ghzMAC");
// 		        if(!updateAddressStatus){
// 		        	console.log("w24ghzMAC Mac Address ("+w24ghzMAC_new+") on device IS NOT UPDATED.");
// 		        	socket.emit('showmacdashboard');
// 		    		return false;
// 		        } else {
// 		        	console.log("w24ghzMAC Mac Address ("+w24ghzMAC_new+") on device IS UPDATED.");
// 		        }
// 		        var updateAddressStatus = await utility.verifyNewMacAddress(w5ghzMAC_new,fourth_mac,"w5ghzMAC");
// 		        if(!updateAddressStatus){
// 		        	console.log("w5ghzMAC Mac Address ("+w5ghzMAC_new+") on device IS NOT UPDATED.");
// 		        	socket.emit('showmacdashboard');
// 		    		return false;
// 		        } else {
// 		        	console.log("w5ghzMAC Mac Address ("+w5ghzMAC_new+") on device IS UPDATED.");
// 		        }

		        console.log("Written!")

		        socket.emit('showmacdashboard');
	    		return false;

		    //   }
		    } catch (err){
		    	console.error(err);
				utility.writemongodberr('Run system',err);
			}

		  }

		  run().catch(error => {
		    console.error(error)
		    process.exit(1)
		  })


}

function doRequest (){
//	console.log("Start tftp server!");
//	tftp.createClient ({ port: 69 }).put (__filename, function (error){
//		console.error (error); //[Error: (Server) Cannot PUT files]
//		server.close ();
//	});
}

function getCurrentAddreses(){

	const os = require('os');
	const interfaces = os.networkInterfaces();
	var html = '<option value="" disabled selected>Please select an IP Address</option>';
	if(interfaces){
//		console.log('interfaces',interfaces);
		for (const [key, interface_value] of Object.entries(interfaces)) {
//			  console.log(key + ": " + interface_value);

				for (const [single_key, interface_val] of Object.entries(interface_value)) {
					if(interface_val.address){
//						console.log(key + ": " + interface_val.address);
						html += '<option value="'+interface_val.address +'">'+key + ": " + interface_val.address +'</option>';
					}

				}
		}
	}

//	console.log('html',html);
	io.emit('setaddresses',html);
}

function getCurrentPorts(){
	const run = async () => {
		const ports = await getPorts();

//		console.log('ports',ports);
		io.emit('setports',ports);

	}

	run().catch(error => {
	  console.error(error)
	  process.exit(1)
	})
}

function closeProccess(){

	process.exit(1);

}



require("openurl").open("http://localhost:8080");





console.log = function(d,val) { //
	if(val){
		io.emit('writelog',d+" "+val);
	} else {
		io.emit('writelog',d);
	}

};
console.error = function(d) { //
	io.emit('errorlog',d);
};



const parse = require('node-html-parser').parse;

const socket_server = http.createServer().listen(9090);
var io = require('socket.io')(socket_server);

io.on('connection', (socket) => {
	  socket.emit('showstartbt');

	  socket.on('startmac', function (port,first_mac,second_mac,third_mac,fourth_mac) {
		  startMacAppProccess(socket,port,first_mac,second_mac,third_mac,fourth_mac);
	  });

	  socket.on('start', function (ip,port,static_ip_type,static_ip_address) {
		  startFirmwareTFTPServer(socket,ip,port,static_ip_type,static_ip_address);
		});

	  socket.on('getaddresses', function () {
		  getCurrentAddreses();
		});

	  socket.on('getports', function () {
		  getCurrentPorts();
		});
	  socket.on('closeproccess', function () {
		  closeProccess();
		});


	});




const server = http.createServer(function (request, response) {

//    console.log('request ', request.url);

    var filePath = '.' + request.url;
    if (filePath == './') {

        filePath = folder_filePath + '/index.html';
    } else {
    	filePath = folder_filePath + request.url;
    }

    var extname = String(path.extname(filePath)).toLowerCase();
    var mimeTypes = {
        '.html': 'text/html',
        '.js': 'text/javascript',
        '.css': 'text/css',
        '.json': 'application/json',
        '.png': 'image/png',
        '.jpg': 'image/jpg',
        '.gif': 'image/gif',
        '.svg': 'image/svg+xml',
        '.wav': 'audio/wav',
        '.mp4': 'video/mp4',
        '.woff': 'application/font-woff',
        '.ttf': 'application/font-ttf',
        '.eot': 'application/vnd.ms-fontobject',
        '.otf': 'application/font-otf',
        '.wasm': 'application/wasm',
        '.exe': 'application/octet-stream',
    };

    var contentType = mimeTypes[extname] || 'application/octet-stream';

    fs.readFile(filePath, function(error, content) {
        if (error) {
            if(error.code == 'ENOENT') {
                fs.readFile('./404.html', function(error, content) {
                    response.writeHead(404, { 'Content-Type': 'text/html' });
                    response.end(content, 'utf-8');
                });
            }
            else {
                response.writeHead(500);
                response.end('Sorry, check with the site admin for error: '+error.code+' ..\n');
            }
        }
        else {
            response.writeHead(200, { 'Content-Type': contentType });

            response.end(content, 'utf-8');
        }
    });

}).listen(8080);





