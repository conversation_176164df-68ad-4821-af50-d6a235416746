// Simple test script for getPCSerial function
const utility = require('../assets/utility');
const { SerialPort } = require('serialport')


async function testGetPCSerial() {
  try {
    console.log('Testing getPCSerial function...');
    const pcSerial = await utility.getPCSerial();
    console.log('PC Serial Number:', pcSerial);

    const port = new SerialPort({
          path: 'COM3',
          baudRate: 115200,
          dataBits: 8,
          parity: 'none',
          stopBits: 1,
          flowControl: false,
          usePromises: true,
      }, function(err) {
          if (err){
              console.log('error: ', err.message)
              port.close()
          } else {

          }

    });




  } catch (error) {
    console.error('Error testing getPCSerial:', error);
  }
}

testGetPCSerial();
